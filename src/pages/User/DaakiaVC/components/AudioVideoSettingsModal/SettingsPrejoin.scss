@import "../../styles/variables";

.settings-prejoin-modal {
  // Auto-sizing modal to prevent horizontal scroll
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    height: 55vh;
    width: auto !important; // Auto width to fit content
    max-width: 95vw; // Prevent modal from exceeding viewport
    max-height: min(450px, 70vh);
    overflow-x: hidden; // Prevent horizontal scrolling
  }

  // Global dropdown consistency - apply to all dropdowns in modal
  .ant-select,
  .custom-device-select,
  .device-settings-dropdown {
    width: 200px !important;
    max-width: 200px !important;
  }

  // Ensure all dropdown text has ellipsis
  .ant-select-selection-item,
  .device-select-text,
  .device-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 160px;
  }

  .ant-modal-header {
    padding: 10px 20px; // Reduced padding
      border-bottom: 1px solid #D7D7D7;
      margin-bottom: 0;
      flex-shrink: 0;

      .ant-modal-title {
        margin: 0;
        padding: 0;
        line-height: 1;
        width: 100%;
        height: 100%;
      }

      .custom-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;

        .custom-modal-title {
          font-size: 16px; // Smaller font
          font-weight: 500;
          color: #636363;
          flex: 1;
        }

        .custom-close-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px; // Smaller button
          height: 28px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
          color: #666;
          font-size: 14px; // Smaller icon

          &:hover {
            background-color: #f5f5f5;
            color: #333;
          }
        }
      }
    }

    // Hide default close button
    .ant-modal-close {
      display: none;
    }

    .ant-modal-body {
      padding: 0;
      height: calc(100% - 60px);
      overflow: hidden;

      .settings-tabs {
        height: 100%;
        display: flex;
        flex-direction: row;

        .ant-tabs-nav {
          width: 180px; // Much smaller sidebar
          margin: 0;
          flex-shrink: 0;
          background-color: #f7f7f7;
          padding: 10px; // Reduced padding


          .ant-tabs-nav-list {
            width: 100%;


            .ant-tabs-tab {
              padding: 6px; // Smaller padding
              margin: 0;
              border-radius: 0;
              width: 100%;
              border-radius: 4px;

              &.ant-tabs-tab-active {
                background-color: #e4e9f5;
                border-color: transparent;

                .tab-label {
                  svg {
                    color: #3B60E4; // Blue color for active tab icons
                    fill: #3B60E4; // For SVG icons
                  }

                  .anticon {
                    color: #3B60E4; // Blue color for active Ant Design icons
                  }

                  span {
                    color: #3B60E4; // Blue color for active tab text
                  }
                }
              }

              .tab-label {
                font-family: $font;
                font-weight: 500;
                font-size: 13px; // Smaller font
                display: flex;
                align-items: center;
                gap: 0.5rem; // Smaller gap
                width: 100%;

                svg {
                  width: 16px; // Smaller icons
                  height: 16px;
                  flex-shrink: 0;
                  color: #3B60E4; // Changed to blue color for all icons
                  fill: #3B60E4; // Changed to blue color for SVG icons
                }

                // Specific styling for Ant Design icons
                .anticon {
                  width: 16px; // Smaller icons
                  height: 16px;
                  flex-shrink: 0;
                  color: #3B60E4; // Changed to blue color for Ant Design icons
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }

                span {
                  white-space: nowrap;
                  margin-left: 0; // Ensure consistent spacing
                  color: #3B60E4; // Changed to blue color for text
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: 2px solid #eff1f4;
          height: 100%;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              padding: 0;
              overflow-y: auto;
              scrollbar-width: thin;
              scrollbar-color: transparent transparent;
              transition: scrollbar-color 0.3s ease;

              // Webkit browsers (Chrome, Safari, Edge) - very thin scrollbar
              &::-webkit-scrollbar {
                width: 2px; // Very thin width
              }

              &::-webkit-scrollbar-track {
                background: transparent;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.3);
                border-radius: 1px;
                min-height: 15px; // Very small minimum height
              }

              &::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.5);
              }
            }
          }
        }

        .ant-tabs-ink-bar {
          display: none;
        }
      }
    }
  }
}

.settings-content {
  padding: 0 0.75rem; // Smaller padding
  height: auto;
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
  transition: scrollbar-color 0.3s ease;

  // Webkit browsers (Chrome, Safari, Edge) - very thin scrollbar
  &::-webkit-scrollbar {
    width: 2px; // Very thin width
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 1px;
    min-height: 15px; // Very small minimum height
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

.settings-section {
  margin-bottom: 1rem; // Smaller margin

  // Add bottom border for microphone, speaker, and camera sections
  &.microphone-settings-section,
  &.speaker-settings-section,
  &.camera-settings-section {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.75rem; // Smaller padding
    margin-bottom: 0.75rem;
  }

  .settings-header {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem; // Smaller gap
    margin-bottom: 1rem; // Smaller margin
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 0px; // Smaller padding

    h3 {
      font-size: 16px; // Smaller font
      font-family: Inter;
      font-weight: 600;
      color: #3B60E4;
      margin: 0;
      line-height: 1;
    }

    .settings-description {
      color: #666;
      font-size: 11px; // Smaller font
      margin: 0;
      line-height: 1;
      padding-bottom: 2px;
    }
  }

  h3 {
    font-size: 22px;
    font-family: $font;
    font-weight: 600;
    color: #000;
    margin-bottom: 0.5rem;
  }

  .settings-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 1.5rem;
  }
}

// Background & Effects Notification Banner
.background-effects-notification {
  background: #E9EEFF;
  border: 1px solid #D2D2D2;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #DDE4FF;
    border-color: #3B60E4;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 96, 228, 0.1);
  }

  .notification-content {
    display: flex;
    align-items: center;
    width: 100%;

    .notification-text {
      font-size: 14px;
      color: #555454;
      line-height: 1.4;

      .notification-link {
        color: #3B60E4;
        font-weight: 600;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// Compact grid-based layout system
.grid-container {
  display: grid;
  gap: 0.5rem; // Smaller gap
  width: 100%;
  margin-bottom: 0.75rem; // Smaller margin

  &.microphone-grid {
    grid-template-columns: minmax(80px, 1fr) minmax(100px, 1.2fr) 2fr; // Smaller columns
    grid-template-rows: repeat(3, auto);
  }

  &.speaker-grid {
    grid-template-columns: minmax(80px, 1fr) minmax(100px, 1.2fr) 2fr; // Smaller columns
    grid-template-rows: repeat(4, auto);
  }

  &.camera-grid {
    grid-template-columns: minmax(80px, 1fr) minmax(100px, 1.2fr) 2fr; // Smaller columns
    grid-template-rows: auto;
  }

  &.enhancement-grid {
    grid-template-columns: minmax(80px, 1fr) minmax(100px, 1.2fr) 2fr; // Smaller columns
    grid-template-rows: auto;
  }

  .grid-cell {
    display: flex;
    align-items: center;
    min-height: 35px; // Smaller height
    padding: 2px 6px; // Smaller padding
    // Remove all visual container styling
    background-color: transparent;
    border: none;
    border-radius: 0;

    &.center {
      justify-content: center;
      text-align: center;
    }

    &.left {
      justify-content: flex-start;
      text-align: left;
    }

    // Special styling for cells with controls - retain original element styles

    .audio-level-blocks,
    .mic-level-indicator,
    .volume-control {
      width: 100%;
      max-width: 100%;
    }

    .audio-level-blocks {
      min-width: auto; // Override the 250px min-width for grid layout
    }

    .mic-level-indicator {
      min-width: auto; // Override the 250px min-width for grid layout
    }

    .volume-control {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
  }
}

.setting-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem; // Smaller margin
  width: 100%;

  &.three-column {
    display: grid;
    grid-template-columns: 140px 1fr 1fr; // Smaller first column
    gap: 0.75rem; // Smaller gap
    align-items: start;
  }

  .setting-label-col {
    display: flex;
    align-items: flex-start;
    height: 100%;
    min-height: 30px; // Smaller height
    padding-top: 0;
  }

  .setting-description-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    min-height: 120px; // Smaller height
  }

  .setting-control-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    min-height: 120px; // Smaller height
  }

  .setting-item {
    display: flex;
    align-items: center;
    min-height: 30px; // Smaller height
    margin-bottom: 0.25rem; // Smaller margin

    &:last-child {
      margin-bottom: 0;
    }
  }

  .setting-label {
    font-weight: 500;
    font-size: 14px; // Smaller font
    color: #555454;
    margin: 0;
  }

  .setting-sublabel {
    font-size: 12px; // Smaller font
    color: #666;
    margin: 0;
  }



  .audio-level-blocks {
    display: flex;
    align-items: center;
    gap: 3px; // Smaller gap
    height: 28px; // Smaller height
    width: 100%;
    min-width: 150px; // Smaller min-width

    .audio-block {
      flex: 1;
      background-color: #e8e8e8;
      border-radius: 2px;
      transition: background-color 0.1s ease-in-out;

      &.active {
        background-color: #3B60E4;
      }
    }
  }
}

// Setting border for visual separation
.setting-border {
  width: 100%;
  height: 1px;
  background-color: #e8e8e8;
  margin: 1rem 0;
}

// Compact Microphone Level Indicator Styles
.mic-level-indicator {
  display: flex;
  align-items: center; // Center align for rectangular bars
  justify-content: center;
  gap: 2px; // Smaller gap
  height: 28px; // Smaller height
  width: 100%;
  min-width: 150px; // Smaller min-width
  padding: 2px 0; // Smaller padding

  .mic-bar {
    flex: 1; // Each bar takes equal share of available width
    height: 18px; // Smaller height
    border-radius: 2px;
    transition: background-color 0.1s ease-in-out;
    background-color: #e8e8e8;
    min-width: 3px; // Smaller minimum width

    &.active {
      background-color: #3B60E4;
    }
  }
}

// Consistent device dropdown styles with fixed width and ellipsis
.device-settings-dropdown {
  width: 200px !important; // Fixed consistent width
  max-width: 200px !important;

  .ant-dropdown-menu {
    background: #FFFFFF;
    border-radius: 8px !important;
    width: 250px; // Slightly wider for dropdown items
    max-width: 250px;
    padding: 4px;
    border: 1px solid #E3E3E3;
    margin-top: 8px;

    .ant-dropdown-menu-item {
      padding: 0;
      margin: 2px 0;
      width: 100%;
      max-width: 240px; // Account for padding

      &:hover {
        background: #F5F5F5;
      }
    }
  }
}

.device-dropdown-item {
  display: flex;
  align-items: center;
  padding: 6px;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
  max-width: 230px; // Consistent max width
  height: 100%;

  &.selected {
    background-color: #E3E3E3;
    padding: 6px;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .device-name {
    font-size: 13px;
    color: #555454;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    max-width: 210px; // Account for padding
  }
}

.custom-device-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #a8a8a8;
  border-radius: 3px;
  padding: 0.4rem 0.8rem;
  font-size: 13px;
  color: #555454;
  font-weight: 500;
  background-color: #fff;
  cursor: pointer;
  width: 200px !important; // Fixed consistent width
  max-width: 200px !important;
  min-height: 40px;

  .device-select-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
    max-width: 160px; // Account for arrow and padding
  }

  .dropdown-arrow {
    color: #555454;
    font-size: 12px;
    flex-shrink: 0;
  }

  &:hover {
    border-color: #999;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;

  .setting-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(90deg, rgba(59, 96, 228, 1) 0%, rgba(86, 123, 255, 1) 100%);
    border-radius: 50%;
    padding: 4px;
    flex-shrink: 0;
  }

  .setting-details {
    display: flex;
    flex-direction: column;

    .setting-label {
      font-size: 16px;
      font-weight: 500;
      color: #555454;
      font-family: $font;
    }

    .setting-sublabel {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
    }
  }

  .setting-label {
    font-size: 16px;
    font-weight: 500;
    color: #555454;
    font-family: $font;
  }
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .ant-select {
    width: 200px !important; // Fixed consistent width
    max-width: 200px !important;

    .ant-select-selector {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      max-width: 200px;

      .ant-select-selection-item {
        max-width: 160px; // Account for arrow space
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover {
        border-color: #3B60E4;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #3B60E4;
      box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.2);
    }
  }



  .ant-switch-checked {
    background-color: #3B60E4 !important;
  }

  .switch-status {
    font-size: 12px;
    color: #666;
    margin-left: 0.5rem;
  }

  .test-button {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background-color: #e6f7ff;
      border-color: #3B60E4;
      color: #3B60E4;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .level-indicator {
    width: 200px;

    .ant-progress {
      .ant-progress-bg {
        transition: width 0.1s ease;
      }
    }
  }

  .brightness-value {
    font-family: $font;
    font-weight: 500;
    color: #666;
    min-width: 40px;
    text-align: right;
    margin-left: 0.5rem;
  }

  // Ensure brightness slider gets blue styling
  .ant-slider {
    width: 200px !important;
    min-width: 200px;

    .ant-slider-rail {
      background-color: #e8e8e8 !important;
      height: 4px !important;
      border-radius: 2px;
      width: 100% !important;
    }

    .ant-slider-track {
      background-color: #3B60E4 !important;
      height: 4px !important;
      border-radius: 2px;
    }

    .ant-slider-handle {
      border: 2px solid #3B60E4 !important;
      background-color: #ffffff !important;
      width: 16px !important;
      height: 16px !important;
      margin-top: -6px !important;

      &:hover {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
      }

      &:focus {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
        box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.2) !important;
      }

      &:active {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
      }
    }

    &:hover {
      .ant-slider-rail {
        background-color: #d9d9d9 !important;
      }

      .ant-slider-track {
        background-color: #3B60E4 !important;
      }

      .ant-slider-handle {
        border-color: #3B60E4 !important;
        background-color: #ffffff !important;
      }
    }
  }
}




// Slider styling - simple blue color
.ant-slider {
  width: 100%;

  // The full slider track (background)
  .ant-slider-rail {
    background-color: #e8e8e8;
    height: 4px;
    border-radius: 2px;
    width: 100%;
  }

  // The active portion of the slider (from start to handle)
  .ant-slider-track {
    background-color: #3B60E4;
    height: 4px;
    border-radius: 2px;
  }

  // The slider handle (the dot you drag)
  .ant-slider-handle {
    border: 2px solid #3B60E4;
    background-color: #ffffff;
    width: 16px;
    height: 16px;
    margin-top: -6px;

    &:hover {
      border-color: #3B60E4;
      background-color: #ffffff;
    }

    &:focus {
      border-color: #3B60E4;
      background-color: #ffffff;
      box-shadow: 0 0 0 2px rgba(59, 96, 228, 0.2);
    }

    &:active {
      border-color: #3B60E4;
      background-color: #ffffff;
    }
  }

  &:hover {
    .ant-slider-rail {
      background-color: #d9d9d9;
    }

    .ant-slider-track {
      background-color: #3B60E4;
    }

    .ant-slider-handle {
      border-color: #3B60E4;
      background-color: #ffffff;
    }
  }
}

// Auto-sizing responsive design - no fixed widths
@media screen and (max-width: 1400px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: auto !important;
      max-width: 90vw !important; // Only limit max width to prevent overflow
    }
  }
}

@media screen and (max-width: 1200px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: auto !important;
      max-width: 85vw !important;
    }
  }
}

@media screen and (max-width: 1000px) {
  .settings-prejoin-modal {
    .ant-modal-content {
      width: auto !important;
      max-width: 90vw !important;
    }
  }
}

// Tablet responsive design (below lg size - up to 1023px, excluding mobile)
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .settings-prejoin-modal.tablet-modal {
    .ant-modal-content {
      width: 90vw !important;
      height: 80vh;
    }

    .ant-modal-body {
      .settings-tabs {
        flex-direction: column;

        .ant-tabs-nav {
          width: 100%;
          background-color: #f7f7f7;
          padding: 0;
          margin-bottom: 0;

          .ant-tabs-nav-list {
            display: flex;
            flex-direction: row;
            justify-content: center;
            width: 100%;

            .ant-tabs-tab {
              flex: 1;
              text-align: center;
              margin: 0;
              padding: 15px 10px;
              border-radius: 0;
              border-bottom: 2px solid transparent;

              &.ant-tabs-tab-active {
                background-color: transparent;
                border-bottom: 2px solid #3B60E4;

                .tab-label {
                  svg, .anticon, span {
                    color: #3B60E4;
                    fill: #3B60E4;
                  }
                }
              }

              .tab-label {
                justify-content: center;
                gap: 0.5rem;

                svg, .anticon {
                  width: 20px;
                  height: 20px;
                }

                span {
                  font-size: 14px;
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: none;
          border-top: 1px solid #eff1f4;
        }
      }
    }
  }
}

// Desktop/XL view - original left sidebar design (1024px+)
@media screen and (min-width: 1024px) {
  .settings-prejoin-modal {
    .ant-modal-body {
      .settings-tabs {
        height: 100%;
        display: flex;
        flex-direction: row;

        .ant-tabs-nav {
          width: 240px;
          margin: 0;
          flex-shrink: 0;
          background-color: #f7f7f7;
          padding: 15px;

          .ant-tabs-nav-list {
            width: 100%;

            .ant-tabs-tab {
              padding: 10px;
              margin: 0;
              border-radius: 5px;
              width: 100%;

              &.ant-tabs-tab-active {
                background-color: #e4e9f5;
                border-color: transparent;

                .tab-label {
                  svg, .anticon, span {
                    color: #3B60E4;
                    fill: #3B60E4;
                  }
                }
              }

              .tab-label {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                width: 100%;

                svg, .anticon {
                  width: 22px;
                  height: 22px;
                  flex-shrink: 0;
                  color: #3B60E4;
                  fill: #3B60E4;
                }

                span {
                  font-size: 16px;
                  font-weight: 500;
                  color: #3B60E4;
                  white-space: nowrap;
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: 2px solid #eff1f4;
          border-top: none;
          height: 100%;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              padding: 0;
              overflow-y: auto;
              scrollbar-width: thin;
              scrollbar-color: transparent transparent;
              transition: scrollbar-color 0.3s ease;

              &::-webkit-scrollbar {
                width: 2px;
              }

              &::-webkit-scrollbar-track {
                background: transparent;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.3);
                border-radius: 1px;
                min-height: 15px;
              }

              &::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.5);
              }
            }
          }
        }
      }
    }
  }
}

// Mobile responsive design (max-width: 767px)
@media screen and (max-width: 767px) {
  .settings-prejoin-modal.mobile-modal {
    // Full screen modal
    .ant-modal-wrap {
      padding: 0 !important;
      height: 100vh !important;
    }

    .ant-modal {
      max-width: 100% !important;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      top: 0 !important;
      height: 100vh !important;
      position: fixed !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
    }

    .ant-modal-content {
      width: 100vw !important;
      height: 100vh !important;
      margin: 0 !important;
      padding: 0 !important;
      border-radius: 0 !important;
      display: flex !important;
      flex-direction: column !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
    }

    .ant-modal-header {
      padding: 12px 16px;
      border-bottom: 1px solid #D7D7D7;
      flex-shrink: 0;

      .custom-modal-header {
        .custom-modal-title {
          font-size: 16px;
          font-weight: 600;
        }

        .custom-close-button {
          width: 32px;
          height: 32px;
          font-size: 14px;
        }
      }
    }

    .ant-modal-body {
      padding: 0;
      height: calc(100vh - 60px);
      overflow: hidden;
      flex: 1;

      .settings-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;

        .ant-tabs-nav {
          width: 100%;
          background-color: transparent;
          padding: 0;
          margin-bottom: 0;
          flex-shrink: 0;

          .ant-tabs-nav-list {
            display: flex;
            justify-content: flex-start;
            padding: 16px 20px 0 20px;

            .ant-tabs-tab {
              padding: 8px 0;
              margin: 0 24px 0 0;
              border-radius: 0;
              width: auto;
              border-bottom: 2px solid transparent;
              background: transparent;

              &.ant-tabs-tab-active {
                background-color: transparent;
                border-color: transparent;
                border-bottom: 2px solid #3B60E4;

                .tab-label {
                  svg, .anticon, span {
                    color: #3B60E4;
                    fill: #3B60E4;
                  }
                }
              }

              .tab-label {
                display: flex;
                align-items: center;
                gap: 8px;

                svg, .anticon {
                  width: 18px;
                  height: 18px;
                  flex-shrink: 0;
                  color: #666;
                  fill: #666;
                }

                span {
                  font-size: 16px;
                  font-weight: 500;
                  color: #666;
                  white-space: nowrap;
                }
              }
            }
          }
        }

        .ant-tabs-content-holder {
          border-left: none;
          border-top: 1px solid #eff1f4;
          height: 100%;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              padding: 0;
              overflow-y: auto;
              scrollbar-width: thin;
              scrollbar-color: transparent transparent;
              transition: scrollbar-color 0.3s ease;

              // Webkit browsers (Chrome, Safari, Edge) - very thin scrollbar
              &::-webkit-scrollbar {
                width: 2px;
              }

              &::-webkit-scrollbar-track {
                background: transparent;
              }

              &::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.3);
                border-radius: 1px;
                min-height: 15px;
              }

              &::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.5);
              }

              .settings-content {
                padding: 0 12px 0 12px;

                .settings-section {
                  margin-bottom: 1rem;

                  .settings-header {
                    margin-bottom: 1rem;
                    padding: 10px 0px 10px 0px;

                    h3 {
                      font-size: 18px;
                    }

                    .settings-description {
                      font-size: 11px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}



@media screen and (max-width: 800px) {
  .settings-prejoin-modal:not(.mobile-modal) {
    .ant-modal-content {
      width: 90vw !important;
      height: 70vh;
    }

    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 220px;
        }
      }
    }
  }

  // Mobile-specific grid responsive design
  .settings-prejoin-modal.mobile-modal {
    .grid-container {
      &.microphone-grid,
      &.speaker-grid,
      &.camera-grid,
      &.enhancement-grid {
        grid-template-columns: 1fr;
        gap: 0;
      }

      .grid-cell {
        min-height: 28px;
        padding: 4px 8px;
        background-color: transparent;
        border: none;

        &.center {
          justify-content: flex-start;
          text-align: left;
        }

        .audio-level-blocks,
        .mic-level-indicator,
        .volume-control {
          max-width: 100%;
        }

        .audio-level-blocks,
        .mic-level-indicator {
          min-width: auto;
          height: 26px;
        }
      }
    }

    .setting-row {
      margin-bottom: 0.3rem;
      padding: 2px 4px;
      min-height: auto;

      &.three-column {
        gap: 0.4rem;
        padding: 2px 0;

        .setting-label-col {
          padding: 4px 0;

          .setting-label {
            font-size: 16px;
            margin-bottom: 0.4rem;
          }
        }

        .setting-description-col {
          padding: 2px 0;

          .setting-item {
            margin-bottom: 0.3rem;
            padding: 2px 0;

            .setting-sublabel {
              font-size: 14px;
            }
          }
        }

        .setting-control-col {
          padding: 2px 0;

          .setting-item {
            margin-bottom: 0.4rem;
            padding: 2px 0;
          }
        }
      }

      // Toggle settings - title and switch on same line
      &:not(.three-column) {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .setting-info {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .setting-details {
            width: 100%;
            margin-bottom: 4px;

            .setting-label {
              font-size: 16px;
              font-weight: 500;
              color: #555454;
              margin: 0;
              text-align: left;
              display: block;
            }
          }

          .setting-sublabel {
            font-size: 13px;
            color: #666;
            margin: 0;
            display: block;
            width: 100%;
            text-align: left;
            line-height: 1.4;
            margin-bottom: 8px;
          }
        }

        // Toggle switch controls (positioned absolutely)
        .setting-control:has(.ant-switch) {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;

          .ant-switch {
            min-width: 32px !important;
            height: 18px !important;

            .ant-switch-handle {
              width: 14px !important;
              height: 14px !important;
              top: 2px !important;

              &::before {
                border-radius: 50% !important;
              }
            }

            &.ant-switch-checked {
              .ant-switch-handle {
                left: calc(100% - 16px) !important;
              }
            }
          }

          .switch-status {
            display: none;
          }
        }

        // Slider controls (full width below text)
        .setting-control:has(.ant-slider) {
          position: static;
          width: 100%;
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 4px;

          .ant-slider {
            flex: 1;
            margin: 0;
          }

          .brightness-value {
            font-size: 14px;
            font-weight: 500;
            color: #555454;
            min-width: 40px;
            text-align: right;
          }
        }
      }
    }

    // Setting border for mobile
    .setting-border {
      margin: 0.3rem 8px;
      height: 1px;
      background-color: #e8e8e8;
    }

    .settings-content {
      padding: 0 12px 0 12px;

      .settings-section {
        margin-bottom: 0.6rem;
        padding: 4px 0;

        .settings-header {
          margin-bottom: 0.6rem;
          padding: 8px 0px 8px 0px;
        }

        &.microphone-settings-section,
        &.speaker-settings-section,
        &.camera-settings-section {
          padding-bottom: 0.6rem;
          margin-bottom: 0.6rem;
        }
      }
    }
  }

  // General grid responsive design for non-mobile
  .grid-container {
    &.microphone-grid,
    &.speaker-grid,
    &.camera-grid,
    &.enhancement-grid {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .grid-cell {
      min-height: 40px;
      padding: 6px;
      background-color: transparent;
      border: none;

      &.center {
        justify-content: flex-start;
        text-align: left;
      }

      .audio-level-blocks,
      .mic-level-indicator,
      .volume-control {
        max-width: 100%;
      }

      .audio-level-blocks,
      .mic-level-indicator {
        min-width: auto; // Override min-width for mobile
      }
    }
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    &.three-column {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .setting-label-col {
        width: 100%;
        padding-top: 0;

        .setting-label {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 1rem;
        }
      }

      .setting-description-col,
      .setting-control-col {
        width: 100%;
      }

      .setting-description-col {
        .setting-item {
          margin-bottom: 0.5rem;

          .setting-sublabel {
            font-size: 16px;
            font-weight: 500;
          }
        }
      }

      .setting-control-col {
        .setting-item {
          margin-bottom: 1rem;
        }
      }
    }
  }

  .setting-control {
    width: 100%;
    justify-content: flex-end;
  }
}

@media screen and (max-width: 600px) {
  .settings-prejoin-modal {
    .ant-modal-body {
      .settings-tabs {
        .ant-tabs-nav {
          width: 160px; // Smaller sidebar

          .ant-tabs-tab {
            padding: 6px; // Smaller padding

            .tab-label {
              gap: 0.25rem; // Smaller gap

              svg {
                width: 14px; // Smaller icons
                height: 14px;
              }

              span {
                font-size: 12px; // Smaller text
              }
            }
          }
        }
      }
    }
  }
}